from langchain.chat_models import init_chat_model
from langchain_core.messages import SystemMessage
from dotenv import load_dotenv
import os

load_dotenv()

model = init_chat_model(
    "openai:deepseek-chat",
    temperature=0
)

json_model = init_chat_model(
    "openai:deepseek-chat",
    temperature=0,  # 设置为0以获得确定性输出
    model_kwargs={
        "response_format": {"type": "json_object"}  # 强制JSON输出
    }
).bind(
    messages=[
        SystemMessage(content="""You are a JSON API that only returns valid JSON objects.
        All responses must be parseable by a JSON parser.
        Always include the word 'json' in your understanding of the task.

        When processing requests, structure your output as valid JSON format.
        Ensure proper JSON syntax with correct quotes, brackets, and commas.
        Do not include any text outside the JSON object.""")
    ]
)

# 导出模型实例
__all__ = ["model", "json_model"]