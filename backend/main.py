import json
from core.llm import model, json_model  # 假设您的文件名为models.py

def test_regular_model():
    """测试普通模型功能"""
    print("=== 测试普通模型 ===")
    
    # 测试普通对话
    response = model.invoke("你好，请介绍一下你自己")
    print("普通模型响应:")
    print(response.content)
    print("-" * 50)
    
    # 测试结构化问题
    response = model.invoke("列出三种编程语言及其主要用途")
    print("结构化问题响应:")
    print(response.content)
    print("-" * 50)

def test_json_model():
    """测试JSON模型功能"""
    print("=== 测试JSON模型 ===")
    
    # 测试简单JSON响应
    try:
        response = json_model.invoke("返回一个包含姓名和年龄的示例JSON")
        print("JSON模型响应:")
        print(response.content)
        
        # 验证是否为有效JSON
        parsed = json.loads(response.content)
        print("✓ 成功解析为JSON对象")
        print(f"JSON结构: {parsed}")
        
    except json.JSONDecodeError as e:
        print("✗ JSON解析失败:", e)
    print("-" * 50)
    
    # 测试结构化数据请求
    try:
        response = json_model.invoke("提供三个城市的信息，包括名称、人口和所属国家")
        print("结构化数据响应:")
        print(response.content)
        
        # 验证是否为有效JSON
        parsed = json.loads(response.content)
        print("✓ 成功解析为JSON对象")
        print(f"数据类型: {type(parsed)}")
        
    except json.JSONDecodeError as e:
        print("✗ JSON解析失败:", e)
    print("-" * 50)
    
    # 测试复杂JSON结构
    try:
        response = json_model.invoke("创建一个用户信息JSON，包含用户基本信息、地址列表和技能数组")
        print("复杂结构响应:")
        print(response.content)
        
        # 验证是否为有效JSON
        parsed = json.loads(response.content)
        print("✓ 成功解析为JSON对象")
        print(f"包含键: {list(parsed.keys())}")
        
    except json.JSONDecodeError as e:
        print("✗ JSON解析失败:", e)
    print("-" * 50)

def test_comparison():
    """对比两种模型的输出差异"""
    print("=== 模型输出对比 ===")
    
    question = "提供两个产品的信息，包括名称、价格和类别"
    
    # 普通模型
    regular_response = model.invoke(question)
    print("普通模型输出:")
    print(regular_response.content)
    print("-" * 30)
    
    # JSON模型
    json_response = json_model.invoke(question)
    print("JSON模型输出:")
    print(json_response.content)
    
    # 验证JSON输出
    try:
        parsed = json.loads(json_response.content)
        print("✓ JSON模型输出验证成功")
    except json.JSONDecodeError:
        print("✗ JSON模型输出不是有效的JSON")

if __name__ == "__main__":
    # 运行所有测试
    test_regular_model()
    test_json_model()
    test_comparison()
    
    print("\n=== 测试完成 ===")