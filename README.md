# LLM JSON Model 配置

这个项目使用 DeepSeek API 和 LangGraph 来实现结构化的 JSON 输出。

## 环境配置

### 1. 安装依赖

```bash
pip install -r backend/requirements.txt
```

### 2. 环境变量配置

复制 `.env.example` 文件为 `.env` 并填入你的 API 密钥：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# DeepSeek API Configuration
DEEPSEEK_API_KEY=your_actual_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Model Configuration
DEEPSEEK_MODEL=deepseek-chat
DEFAULT_TEMPERATURE=0
DEFAULT_MAX_TOKENS=4096
```

## 使用方法

### 基础模型

```python
from backend.core.llm import model

response = model.invoke("你好，世界！")
print(response.content)
```

### JSON 输出模型

```python
from backend.core.llm import json_model_with_prompt
from langchain_core.messages import HumanMessage

# 使用 JSON 模型进行结构化输出
response = json_model_with_prompt.invoke([
    HumanMessage(content="""
    Extract information from: "John is 25 years old and works as a developer"
    Return JSON with: name, age, profession
    """)
])

import json
result = json.loads(response.content)
print(result)
```

### 在 LangGraph 中使用

```python
from langgraph.graph import StateGraph
from backend.core.llm import json_model_with_prompt

def process_node(state):
    response = json_model_with_prompt.invoke([
        HumanMessage(content=f"Process this data as JSON: {state['input']}")
    ])
    
    try:
        result = json.loads(response.content)
        return {"output": result}
    except json.JSONDecodeError:
        return {"error": "Invalid JSON response"}

# 在 StateGraph 中使用
graph = StateGraph(...)
graph.add_node("process", process_node)
```

## 重要注意事项

1. **JSON 格式要求**: 在使用 JSON 输出模型时，prompt 中必须包含 "json" 关键词
2. **最大令牌数**: 合理设置 `max_tokens` 参数，防止 JSON 字符串被截断
3. **错误处理**: 始终使用 try-catch 来处理 JSON 解析错误
4. **环境变量**: 不要将 API 密钥硬编码在代码中，使用环境变量

## 示例

查看 `backend/examples/json_model_usage.py` 文件获取更多使用示例。

## 文件结构

```
.
├── .env                          # 环境变量配置（不提交到 git）
├── .env.example                  # 环境变量模板
├── .gitignore                    # Git 忽略文件
├── README.md                     # 项目说明
├── backend/
│   ├── core/
│   │   └── llm.py               # LLM 模型配置
│   ├── examples/
│   │   └── json_model_usage.py  # 使用示例
│   └── requirements.txt         # Python 依赖
```
